import{r as a,c9 as i,O as o}from"./index.C1z8KpLA.js";class d{manageFormClearListener(r,s,t){o(this.formClearListener)&&this.lastWidgetMgr===r&&this.lastFormId===s||(this.disconnect(),i(s)&&(this.formClearListener=r.addFormClearedListener(s,t),this.lastWidgetMgr=r,this.lastFormId=s))}disconnect(){var r;(r=this.formClearListener)==null||r.disconnect(),this.formClearListener=void 0,this.lastWidgetMgr=void 0,this.lastFormId=void 0}}function l({element:e,widgetMgr:r,onFormCleared:s}){a.useEffect(()=>{if(!i(e.formId))return;const t=r.addFormClearedListener(e.formId,s);return()=>{t.disconnect()}},[e,r,s])}export{d as F,l as u};
