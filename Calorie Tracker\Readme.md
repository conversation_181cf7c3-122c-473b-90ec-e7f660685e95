# 🍎 NutriSight

A modern Streamlit web application that analyzes food images to estimate calories and macronutrients using the Gemma 3 model via OpenRouter API.

## Features

- 📷 **Webcam Capture**: Take photos of your food directly in the browser
- 📁 **Image Upload**: Upload existing food photos (JPG, PNG)
- 🔍 **AI Analysis**: Powered by Gemma 3 for accurate nutritional estimation
- 📊 **Detailed Reports**: Get calories, macros, and percentage breakdowns
- 📱 **Mobile-Friendly**: Responsive design that works on all devices
- ⚡ **Fast Processing**: In-memory image processing for optimal performance

## Installation

### Prerequisites
- Python 3.9 or higher
- A modern web browser with webcam support (for photo capture)

### Setup Steps

1. **Clone or download the project files**
   ```bash
   # Create a new directory
   mkdir calorie-tracker
   cd calorie-tracker
   
   # Copy the provided files:
   # - app.py
   # - requirements.txt
   # - README.md
   ```

2. **Create a virtual environment (recommended)**
   ```bash
   python -m venv venv
   
   # On Windows
   venv\Scripts\activate
   
   # On macOS/Linux
   source venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

## Running the Application

1. **Start the Streamlit app**
   ```bash
   streamlit run app.py
   ```

2. **Open your browser**
   - The app will automatically open at `http://localhost:8501`
   - If it doesn't open automatically, navigate to the URL manually

3. **Use the application**
   - Choose "Capture Photo" to take a webcam photo, or "Upload Image" to select a file
   - Click "Analyze Nutrition" to get your results
   - View the detailed nutritional breakdown

## API Configuration

### OpenRouter API Key Setup

**⚠️ Important:** If you're getting a 401 "No auth credentials found" error, follow these steps:

#### Option 1: Environment Variable (Recommended)
1. Get your API key from [OpenRouter](https://openrouter.ai/keys)
2. Create a `.env` file in the project directory:
   ```bash
   OPENROUTER_API_KEY=your_actual_api_key_here
   ```
3. The app will automatically use the environment variable

#### Option 2: Direct Code Replacement
Replace the hardcoded key in `App.py`:
```python
OPENROUTER_API_KEY = "your_actual_api_key_here"
```

#### Testing Your API Key
The app includes a built-in API key tester:
1. Run the application
2. Expand the "🔑 API Configuration" section
3. Click "Test API Key" to verify it's working

### Gemma 3 Endpoint Details
- **API URL**: `https://openrouter.ai/api/v1/chat/completions`
- **Model**: `google/gemma-2-9b-it:free`
- **Image Format**: Base64-encoded JPEG
- **Max Tokens**: 1000
- **Temperature**: 0.3 (for consistent results)
- **Required Headers**: Authorization, Content-Type, HTTP-Referer, X-Title

### Request Format
The application sends images to Gemma 3 with this structure:
```json
{
  "model": "google/gemma-2-9b-it:free",
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "nutritional analysis prompt"},
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,<base64_image>"
          }
        }
      ]
    }
  ],
  "max_tokens": 1000,
  "temperature": 0.3
}
```

## Project Structure

```
calorie-tracker/
├── app.py              # Main Streamlit application
├── requirements.txt    # Python dependencies
└── README.md          # This file
```

## Key Components

### Core Functions

- **`image_to_base64(image)`**: Converts PIL images to base64 format
- **`call_gemma3(image_base64)`**: Sends API requests to OpenRouter/Gemma 3
- **`parse_text_response(text)`**: Fallback parser for non-JSON responses
- **`calculate_macro_percentages(macros, calories)`**: Computes macro distribution
- **`render_report(analysis_result)`**: Displays formatted nutritional results

### Video Processing
- **`VideoProcessor`**: Handles webcam frame capture using streamlit-webrtc
- Real-time video streaming with capture functionality
- Automatic BGR to RGB conversion for PIL compatibility

## Performance Optimizations

- **In-Memory Processing**: No temporary files created
- **Efficient Image Encoding**: JPEG compression with 85% quality
- **Async-Ready**: Built for concurrent user sessions
- **Error Handling**: Comprehensive error management and user feedback

## Browser Compatibility

### Webcam Support
- Chrome/Chromium (recommended)
- Firefox
- Safari (macOS)
- Edge

### HTTPS Requirement
For webcam access in production, the app must be served over HTTPS. For local development, `localhost` works without HTTPS.

## Troubleshooting

### Common Issues

1. **Webcam not working**
   - Ensure your browser has camera permissions
   - Try using Chrome/Chromium browsers
   - Check if other applications are using the camera

2. **API errors**
   - Verify the OpenRouter API key is valid
   - Check your internet connection
   - Ensure the image file isn't corrupted

3. **Dependencies issues**
   - Make sure you're using Python 3.9+
   - Try upgrading pip: `pip install --upgrade pip`
   - Install packages individually if batch install fails

4. **Performance issues**
   - Large images are automatically compressed
   - Close other browser tabs if memory is limited
   - Restart the app if it becomes unresponsive

### Error Messages

#### Authentication Errors (401)
- **"No auth credentials found"**:
  - **Cause**: API key is missing, invalid, or expired
  - **Solution**: Get a new API key from [OpenRouter](https://openrouter.ai/keys) and update your configuration
  - **Check**: Use the built-in API key tester in the app

#### Other Common Errors
- **"Request timed out"**: API is taking too long, try again
- **"Error converting image"**: Image format may be unsupported (use JPG/PNG)
- **"Failed to capture photo"**: Check webcam permissions in your browser
- **"API Error: 429"**: Rate limit exceeded, wait a moment and try again
- **"API Error: 402"**: Insufficient credits in your OpenRouter account

## Security Notes

- The API key is embedded in the code for demonstration purposes
- In production, use environment variables: `OPENROUTER_API_KEY=your_key_here`
- Images are processed in memory and not stored permanently
- No user data is retained between sessions

## Limitations

- Nutritional estimates are approximate
- Works best with clear, well-lit food photos
- Accuracy depends on Gemma 3's training data
- Free tier API may have rate limits

## License

This project is provided as-is for educational and personal use.

## Support

For issues with:
- **Streamlit**: Visit [Streamlit Documentation](https://docs.streamlit.io/)
- **OpenRouter API**: Check [OpenRouter Documentation](https://openrouter.ai/docs)
- **WebRTC**: See [streamlit-webrtc docs](https://github.com/whitphx/streamlit-webrtc)