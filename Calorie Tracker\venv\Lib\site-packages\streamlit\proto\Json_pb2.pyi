"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class Json(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BODY_FIELD_NUMBER: builtins.int
    EXPANDED_FIELD_NUMBER: builtins.int
    MAX_EXPAND_DEPTH_FIELD_NUMBER: builtins.int
    body: builtins.str
    """Content to display."""
    expanded: builtins.bool
    """Controls the initial expansion state of the json element.
    Is superseded by max_expand_depth if provided.
    """
    max_expand_depth: builtins.int
    """The maximum depth to expand the JSON object."""
    def __init__(
        self,
        *,
        body: builtins.str = ...,
        expanded: builtins.bool = ...,
        max_expand_depth: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_max_expand_depth", b"_max_expand_depth", "max_expand_depth", b"max_expand_depth"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_max_expand_depth", b"_max_expand_depth", "body", b"body", "expanded", b"expanded", "max_expand_depth", b"max_expand_depth"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["_max_expand_depth", b"_max_expand_depth"]) -> typing.Literal["max_expand_depth"] | None: ...

global___Json = Json
