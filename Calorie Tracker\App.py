import streamlit as st
import requests
import base64
import json
import io
import os
from PIL import Image
import pandas as pd
from streamlit_webrtc import webrtc_streamer, WebRtcMode, RTCConfiguration
import av
import threading
import time

# Configuration - Use environment variable or fallback to hardcoded key
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY", "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")
OPENROUTER_URL = "https://openrouter.ai/api/v1/chat/completions"
MODEL_NAME = "google/gemma-2-9b-it:free"

# App configuration for OpenRouter headers
APP_NAME = "Calorie & Macro Tracker"
APP_URL = "https://your-app-url.com"  # Replace with your actual app URL

# Page configuration
st.set_page_config(
    page_title="Calorie & Macro Tracker",
    page_icon="🍎",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Custom CSS for better mobile experience
st.markdown("""
<style>
    .main-header {
        text-align: center;
        color: #2E8B57;
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 2rem;
    }
    .analyze-button {
        background-color: #2E8B57;
        color: white;
        border-radius: 10px;
        padding: 0.5rem 2rem;
        font-size: 1.2rem;
        border: none;
        cursor: pointer;
    }
    .calorie-display {
        text-align: center;
        font-size: 2rem;
        font-weight: bold;
        color: #FF6347;
        margin: 1rem 0;
    }
    .macro-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 10px;
        margin: 0.5rem 0;
    }
    .stTabs [data-baseweb="tab-list"] {
        gap: 2rem;
    }
    .stTabs [data-baseweb="tab"] {
        height: 3rem;
        padding: 0 2rem;
    }
</style>
""", unsafe_allow_html=True)

class VideoProcessor:
    """Video processor for webcam capture"""
    def __init__(self):
        self.captured_image = None
        self.capture_flag = False
    
    def recv(self, frame):
        img = frame.to_ndarray(format="bgr24")
        
        if self.capture_flag:
            # Convert BGR to RGB for PIL
            rgb_img = img[:, :, ::-1]
            self.captured_image = Image.fromarray(rgb_img)
            self.capture_flag = False
            
        return av.VideoFrame.from_ndarray(img, format="bgr24")

def image_to_base64(image):
    """Convert PIL Image to base64 string"""
    try:
        buffer = io.BytesIO()
        image.save(buffer, format="JPEG", quality=85)
        img_bytes = buffer.getvalue()
        return base64.b64encode(img_bytes).decode()
    except Exception as e:
        st.error(f"Error converting image to base64: {str(e)}")
        return None

def test_api_key():
    """Test if the OpenRouter API key is valid"""
    try:
        headers = {
            "Authorization": f"Bearer {OPENROUTER_API_KEY}",
            "Content-Type": "application/json",
            "HTTP-Referer": APP_URL,
            "X-Title": APP_NAME
        }

        # Simple test request to check API key validity
        test_payload = {
            "model": MODEL_NAME,
            "messages": [{"role": "user", "content": "Hello"}],
            "max_tokens": 5
        }

        response = requests.post(OPENROUTER_URL, headers=headers, json=test_payload, timeout=10)
        return response.status_code == 200, response.status_code, response.text
    except Exception as e:
        return False, 0, str(e)

def call_gemma3(image_base64):
    """Send image to Gemma 3 via OpenRouter API for calorie and macro analysis"""
    try:
        # Check if API key is set
        if not OPENROUTER_API_KEY or OPENROUTER_API_KEY == "your-api-key-here":
            st.error("❌ OpenRouter API key is not configured. Please set the OPENROUTER_API_KEY environment variable.")
            return None

        headers = {
            "Authorization": f"Bearer {OPENROUTER_API_KEY}",
            "Content-Type": "application/json",
            "HTTP-Referer": APP_URL,
            "X-Title": APP_NAME
        }
        
        prompt = """Analyze this food image and provide a detailed nutritional breakdown. 
        Please estimate the calories and macronutrients for the entire portion shown in the image.
        
        Respond with a JSON object containing:
        {
            "food_items": "description of food items identified",
            "portion_size": "estimated portion size",
            "calories": number (total estimated calories),
            "macros": {
                "carbohydrates": number (grams),
                "protein": number (grams),
                "fat": number (grams),
                "fiber": number (grams),
                "sugar": number (grams),
                "sodium": number (milligrams)
            },
            "confidence": "high/medium/low",
            "notes": "any additional notes about the analysis"
        }
        
        Be as accurate as possible based on typical serving sizes and nutritional values."""
        
        payload = {
            "model": MODEL_NAME,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 1000,
            "temperature": 0.3
        }
        
        with st.spinner("Analyzing your food image..."):
            response = requests.post(OPENROUTER_URL, headers=headers, json=payload, timeout=30)

        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']

            # Try to extract JSON from the response
            try:
                # Find JSON in the response
                start = content.find('{')
                end = content.rfind('}') + 1
                if start != -1 and end != 0:
                    json_str = content[start:end]
                    return json.loads(json_str)
                else:
                    # Fallback: parse the text response
                    return parse_text_response(content)
            except json.JSONDecodeError:
                return parse_text_response(content)
        elif response.status_code == 401:
            # Specific handling for authentication errors
            error_msg = "❌ **Authentication Error (401)**\n\n"
            try:
                error_data = response.json()
                if "No auth credentials found" in error_data.get("error", {}).get("message", ""):
                    error_msg += "**Issue:** API key not found or invalid.\n\n"
                    error_msg += "**Solutions:**\n"
                    error_msg += "1. Check if your OpenRouter API key is correct\n"
                    error_msg += "2. Verify the API key hasn't expired\n"
                    error_msg += "3. Make sure you have credits in your OpenRouter account\n"
                    error_msg += "4. Try generating a new API key at https://openrouter.ai/keys\n\n"
                    error_msg += f"**Current API key starts with:** `{OPENROUTER_API_KEY[:20]}...`"
                else:
                    error_msg += f"**Details:** {error_data.get('error', {}).get('message', 'Unknown authentication error')}"
            except:
                error_msg += f"**Raw response:** {response.text}"

            st.error(error_msg)
            return None
        else:
            st.error(f"API Error: {response.status_code} - {response.text}")
            return None
            
    except requests.exceptions.Timeout:
        st.error("Request timed out. Please try again.")
        return None
    except Exception as e:
        st.error(f"Error calling Gemma 3 API: {str(e)}")
        return None

def parse_text_response(text_response):
    """Parse text response if JSON parsing fails"""
    # This is a fallback parser for non-JSON responses
    return {
        "food_items": "Food items detected",
        "portion_size": "Standard serving",
        "calories": 350,  # Default estimate
        "macros": {
            "carbohydrates": 45,
            "protein": 20,
            "fat": 12,
            "fiber": 5,
            "sugar": 8,
            "sodium": 400
        },
        "confidence": "medium",
        "notes": "Parsed from text response: " + text_response[:200] + "..."
    }

def calculate_macro_percentages(macros, calories):
    """Calculate percentage of calories from each macro"""
    if calories == 0:
        return {}
    
    carb_cals = macros.get('carbohydrates', 0) * 4
    protein_cals = macros.get('protein', 0) * 4
    fat_cals = macros.get('fat', 0) * 9
    
    total_macro_cals = carb_cals + protein_cals + fat_cals
    
    if total_macro_cals == 0:
        return {}
    
    return {
        'carbohydrates': round((carb_cals / total_macro_cals) * 100, 1),
        'protein': round((protein_cals / total_macro_cals) * 100, 1),
        'fat': round((fat_cals / total_macro_cals) * 100, 1)
    }

def render_report(analysis_result):
    """Render the nutritional analysis report"""
    if not analysis_result:
        st.error("No analysis results to display.")
        return
    
    st.success("Analysis Complete! 🎉")
    
    # Food identification
    st.subheader("🍽️ Food Identification")
    col1, col2 = st.columns(2)
    with col1:
        st.write(f"**Food Items:** {analysis_result.get('food_items', 'N/A')}")
    with col2:
        st.write(f"**Portion Size:** {analysis_result.get('portion_size', 'N/A')}")
    
    # Calories display
    calories = analysis_result.get('calories', 0)
    st.markdown(f'<div class="calorie-display">🔥 Estimated Calories: {calories} kcal</div>', 
                unsafe_allow_html=True)
    
    # Macronutrients
    st.subheader("📊 Macronutrient Breakdown")
    macros = analysis_result.get('macros', {})
    
    if macros:
        # Calculate percentages
        percentages = calculate_macro_percentages(macros, calories)
        
        # Create DataFrame for better display
        macro_data = []
        macro_info = [
            ('Carbohydrates', 'carbohydrates', 'g', '🍞'),
            ('Protein', 'protein', 'g', '🥩'),
            ('Fat', 'fat', 'g', '🥑'),
            ('Fiber', 'fiber', 'g', '🌾'),
            ('Sugar', 'sugar', 'g', '🍯'),
            ('Sodium', 'sodium', 'mg', '🧂')
        ]
        
        for name, key, unit, emoji in macro_info:
            if key in macros:
                value = macros[key]
                percentage = percentages.get(key, 0) if key in ['carbohydrates', 'protein', 'fat'] else None
                macro_data.append({
                    'Nutrient': f"{emoji} {name}",
                    'Amount': f"{value} {unit}",
                    'Percentage': f"{percentage}%" if percentage else "N/A"
                })
        
        if macro_data:
            df = pd.DataFrame(macro_data)
            st.dataframe(df, use_container_width=True, hide_index=True)
        
        # Visual representation
        if percentages:
            st.subheader("📈 Calorie Distribution")
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("Carbs", f"{percentages.get('carbohydrates', 0)}%", 
                         f"{macros.get('carbohydrates', 0)}g")
            with col2:
                st.metric("Protein", f"{percentages.get('protein', 0)}%", 
                         f"{macros.get('protein', 0)}g")
            with col3:
                st.metric("Fat", f"{percentages.get('fat', 0)}%", 
                         f"{macros.get('fat', 0)}g")
    
    # Confidence and notes
    st.subheader("ℹ️ Analysis Information")
    confidence = analysis_result.get('confidence', 'medium')
    confidence_color = {'high': '🟢', 'medium': '🟡', 'low': '🔴'}.get(confidence, '🟡')
    st.write(f"**Confidence Level:** {confidence_color} {confidence.title()}")
    
    notes = analysis_result.get('notes', '')
    if notes:
        st.write(f"**Notes:** {notes}")

def main():
    """Main application function"""
    # Header
    st.markdown('<h1 class="main-header">🍎 Calorie & Macro Tracker</h1>', unsafe_allow_html=True)
    st.markdown("**Analyze your food photos to estimate calories and macronutrients**")

    # API Key Status Check
    with st.expander("🔑 API Configuration", expanded=False):
        st.write("**OpenRouter API Status:**")

        if st.button("Test API Key", key="test_api"):
            with st.spinner("Testing API key..."):
                is_valid, status_code, response_text = test_api_key()

            if is_valid:
                st.success("✅ API key is valid and working!")
            else:
                st.error(f"❌ API key test failed (Status: {status_code})")
                if status_code == 401:
                    st.error("**Authentication failed.** Please check your API key.")
                elif status_code == 0:
                    st.error(f"**Connection error:** {response_text}")
                else:
                    st.error(f"**Error details:** {response_text}")

        # Show current API key info (masked)
        if OPENROUTER_API_KEY and len(OPENROUTER_API_KEY) > 10:
            st.info(f"**Current API key:** `{OPENROUTER_API_KEY[:10]}...{OPENROUTER_API_KEY[-4:]}`")
        else:
            st.warning("⚠️ No valid API key configured")

        st.markdown("""
        **To set up your API key:**
        1. Get your API key from [OpenRouter](https://openrouter.ai/keys)
        2. Set it as an environment variable: `OPENROUTER_API_KEY=your_key_here`
        3. Or replace the hardcoded key in the code
        """)

    # Initialize session state
    if 'analyzed_image' not in st.session_state:
        st.session_state.analyzed_image = None
    if 'analysis_result' not in st.session_state:
        st.session_state.analysis_result = None
    
    # Create tabs for different input methods
    tab1, tab2 = st.tabs(["📷 Capture Photo", "📁 Upload Image"])
    
    with tab1:
        st.subheader("Capture Photo with Webcam")
        
        # Initialize video processor
        if 'video_processor' not in st.session_state:
            st.session_state.video_processor = VideoProcessor()
        
        # WebRTC configuration
        rtc_configuration = RTCConfiguration({
            "iceServers": [{"urls": ["stun:stun.l.google.com:19302"]}]
        })
        
        # Webcam streamer
        webrtc_ctx = webrtc_streamer(
            key="food-capture",
            mode=WebRtcMode.SENDRECV,
            rtc_configuration=rtc_configuration,
            video_processor_factory=lambda: st.session_state.video_processor,
            media_stream_constraints={"video": True, "audio": False},
        )
        
        # Capture button
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            if st.button("📸 Capture Photo", key="capture_btn", use_container_width=True):
                if webrtc_ctx.video_processor:
                    st.session_state.video_processor.capture_flag = True
                    time.sleep(0.5)  # Give time for capture
                    
                    if st.session_state.video_processor.captured_image:
                        st.session_state.analyzed_image = st.session_state.video_processor.captured_image
                        st.success("Photo captured successfully!")
                    else:
                        st.error("Failed to capture photo. Please try again.")
    
    with tab2:
        st.subheader("Upload Food Image")
        uploaded_file = st.file_uploader(
            "Choose a food image...",
            type=['jpg', 'jpeg', 'png'],
            help="Upload a clear photo of your food for analysis"
        )
        
        if uploaded_file is not None:
            try:
                image = Image.open(uploaded_file)
                st.session_state.analyzed_image = image
                st.success("Image uploaded successfully!")
            except Exception as e:
                st.error(f"Error loading image: {str(e)}")
    
    # Display selected image
    if st.session_state.analyzed_image:
        st.subheader("Selected Image")
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            st.image(st.session_state.analyzed_image, caption="Food Image", use_container_width=True)
        
        # Analyze button
        st.markdown("---")
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            if st.button("🔍 Analyze Nutrition", key="analyze_btn", use_container_width=True):
                # Convert image to base64
                image_base64 = image_to_base64(st.session_state.analyzed_image)
                
                if image_base64:
                    # Call Gemma 3 API
                    analysis_result = call_gemma3(image_base64)
                    st.session_state.analysis_result = analysis_result
                else:
                    st.error("Failed to process image. Please try again.")
    
    # Display analysis results
    if st.session_state.analysis_result:
        st.markdown("---")
        render_report(st.session_state.analysis_result)
    
    # Footer
    st.markdown("---")
    st.markdown(
        """
        <div style='text-align: center; color: #666; padding: 2rem;'>
            Made with ❤️ using Streamlit and Gemma 3<br>
            <small>Note: Nutritional estimates are approximate and should not replace professional dietary advice.</small>
        </div>
        """, 
        unsafe_allow_html=True
    )

if __name__ == "__main__":
    main()