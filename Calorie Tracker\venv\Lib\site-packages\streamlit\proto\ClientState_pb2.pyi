"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import streamlit.proto.WidgetStates_pb2
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class ContextInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TIMEZONE_FIELD_NUMBER: builtins.int
    TIMEZONE_OFFSET_FIELD_NUMBER: builtins.int
    LOCALE_FIELD_NUMBER: builtins.int
    URL_FIELD_NUMBER: builtins.int
    IS_EMBEDDED_FIELD_NUMBER: builtins.int
    timezone: builtins.str
    timezone_offset: builtins.int
    locale: builtins.str
    url: builtins.str
    is_embedded: builtins.bool
    def __init__(
        self,
        *,
        timezone: builtins.str | None = ...,
        timezone_offset: builtins.int | None = ...,
        locale: builtins.str | None = ...,
        url: builtins.str | None = ...,
        is_embedded: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_is_embedded", b"_is_embedded", "_locale", b"_locale", "_timezone", b"_timezone", "_timezone_offset", b"_timezone_offset", "_url", b"_url", "is_embedded", b"is_embedded", "locale", b"locale", "timezone", b"timezone", "timezone_offset", b"timezone_offset", "url", b"url"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_is_embedded", b"_is_embedded", "_locale", b"_locale", "_timezone", b"_timezone", "_timezone_offset", b"_timezone_offset", "_url", b"_url", "is_embedded", b"is_embedded", "locale", b"locale", "timezone", b"timezone", "timezone_offset", b"timezone_offset", "url", b"url"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_is_embedded", b"_is_embedded"]) -> typing.Literal["is_embedded"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_locale", b"_locale"]) -> typing.Literal["locale"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_timezone", b"_timezone"]) -> typing.Literal["timezone"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_timezone_offset", b"_timezone_offset"]) -> typing.Literal["timezone_offset"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_url", b"_url"]) -> typing.Literal["url"] | None: ...

global___ContextInfo = ContextInfo

@typing.final
class ClientState(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    QUERY_STRING_FIELD_NUMBER: builtins.int
    WIDGET_STATES_FIELD_NUMBER: builtins.int
    PAGE_SCRIPT_HASH_FIELD_NUMBER: builtins.int
    PAGE_NAME_FIELD_NUMBER: builtins.int
    FRAGMENT_ID_FIELD_NUMBER: builtins.int
    IS_AUTO_RERUN_FIELD_NUMBER: builtins.int
    CACHED_MESSAGE_HASHES_FIELD_NUMBER: builtins.int
    CONTEXT_INFO_FIELD_NUMBER: builtins.int
    query_string: builtins.str
    page_script_hash: builtins.str
    page_name: builtins.str
    fragment_id: builtins.str
    is_auto_rerun: builtins.bool
    @property
    def widget_states(self) -> streamlit.proto.WidgetStates_pb2.WidgetStates: ...
    @property
    def cached_message_hashes(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """List of hashes of messages that are currently cached in
        the frontend forward message cache.
        """

    @property
    def context_info(self) -> global___ContextInfo: ...
    def __init__(
        self,
        *,
        query_string: builtins.str = ...,
        widget_states: streamlit.proto.WidgetStates_pb2.WidgetStates | None = ...,
        page_script_hash: builtins.str = ...,
        page_name: builtins.str = ...,
        fragment_id: builtins.str = ...,
        is_auto_rerun: builtins.bool = ...,
        cached_message_hashes: collections.abc.Iterable[builtins.str] | None = ...,
        context_info: global___ContextInfo | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["context_info", b"context_info", "widget_states", b"widget_states"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["cached_message_hashes", b"cached_message_hashes", "context_info", b"context_info", "fragment_id", b"fragment_id", "is_auto_rerun", b"is_auto_rerun", "page_name", b"page_name", "page_script_hash", b"page_script_hash", "query_string", b"query_string", "widget_states", b"widget_states"]) -> None: ...

global___ClientState = ClientState
