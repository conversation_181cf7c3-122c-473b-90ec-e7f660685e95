import{d as Ze,bw as J,r as x,b_ as mt,bN as bt,bx as Z,b$ as yt,c0 as Tt,n as me,J as kt,bF as Ot,z as wt,aM as Rt,C as ze,j as re,R as Ve,cp as St,bs as Mt,bG as _t,bt as $t,bb as Et,bu as It,S as xt,cq as $e}from"./index.C1z8KpLA.js";import{s as Ne}from"./sprintf.D7DtBTRn.js";import{a as Ct}from"./useBasicWidgetState.zXY9CjFS.js";import"./FormClearHelper.B67tgll0.js";var se={},H={},ue={},ce={},Ue;function Ie(){if(Ue)return ce;Ue=1,Object.defineProperty(ce,"__esModule",{value:!0}),ce.Direction=void 0;var e;return function(r){r.Right="to right",r.Left="to left",r.Down="to bottom",r.Up="to top"}(e||(ce.Direction=e={})),ce}var He;function Je(){return He||(He=1,function(e){var r=ue&&ue.__spreadArray||function(i,o,u){if(u||arguments.length===2)for(var h=0,b=o.length,g;h<b;h++)(g||!(h in o))&&(g||(g=Array.prototype.slice.call(o,0,h)),g[h]=o[h]);return i.concat(g||Array.prototype.slice.call(o))};Object.defineProperty(e,"__esModule",{value:!0}),e.isIOS=e.useThumbOverlap=e.assertUnreachable=e.voidFn=e.getTrackBackground=e.replaceAt=e.schd=e.translate=e.getClosestThumbIndex=e.translateThumbs=e.getPaddingAndBorder=e.getMargin=e.checkInitialOverlap=e.checkValuesAgainstBoundaries=e.checkBoundaries=e.isVertical=e.relativeValue=e.normalizeValue=e.isStepDivisible=e.isTouchEvent=e.getStepDecimals=void 0;var a=Ze(),s=Ie(),p=function(i){var o=i.toString().split(".")[1];return o?o.length:0};e.getStepDecimals=p;function S(i){return i.touches&&i.touches.length||i.changedTouches&&i.changedTouches.length}e.isTouchEvent=S;function c(i,o,u){var h=(o-i)/u,b=8,g=Number(h.toFixed(b));return parseInt(g.toString(),10)===g}e.isStepDivisible=c;function f(i,o,u,h,b,g,O){var _=1e11;if(i=Math.round(i*_)/_,!g){var P=O[o-1],I=O[o+1];if(P&&P>i)return P;if(I&&I<i)return I}if(i>h)return h;if(i<u)return u;var U=Math.floor(i*_-u*_)%Math.floor(b*_),V=Math.floor(i*_-Math.abs(U)),W=U===0?i:V/_,A=Math.abs(U/_)<b/2?W:W+b,L=(0,e.getStepDecimals)(b);return parseFloat(A.toFixed(L))}e.normalizeValue=f;function j(i,o,u){return(i-o)/(u-o)}e.relativeValue=j;function F(i){return i===s.Direction.Up||i===s.Direction.Down}e.isVertical=F;function B(i,o,u){if(o>=u)throw new RangeError("min (".concat(o,") is equal/bigger than max (").concat(u,")"));if(i<o)throw new RangeError("value (".concat(i,") is smaller than min (").concat(o,")"));if(i>u)throw new RangeError("value (".concat(i,") is bigger than max (").concat(u,")"))}e.checkBoundaries=B;function $(i,o,u){return i<o?o:i>u?u:i}e.checkValuesAgainstBoundaries=$;function T(i){if(!(i.length<2)&&!i.slice(1).every(function(o,u){return i[u]<=o}))throw new RangeError("values={[".concat(i,"]} needs to be sorted when allowOverlap={false}"))}e.checkInitialOverlap=T;function d(i){var o=window.getComputedStyle(i);return{top:parseInt(o["margin-top"],10),bottom:parseInt(o["margin-bottom"],10),left:parseInt(o["margin-left"],10),right:parseInt(o["margin-right"],10)}}e.getMargin=d;function t(i){var o=window.getComputedStyle(i);return{top:parseInt(o["padding-top"],10)+parseInt(o["border-top-width"],10),bottom:parseInt(o["padding-bottom"],10)+parseInt(o["border-bottom-width"],10),left:parseInt(o["padding-left"],10)+parseInt(o["border-left-width"],10),right:parseInt(o["padding-right"],10)+parseInt(o["border-right-width"],10)}}e.getPaddingAndBorder=t;function n(i,o,u){var h=u?-1:1;i.forEach(function(b,g){return v(b,h*o[g].x,o[g].y)})}e.translateThumbs=n;function l(i,o,u,h){for(var b=0,g=C(i[0],o,u,h),O=1;O<i.length;O++){var _=C(i[O],o,u,h);_<g&&(g=_,b=O)}return b}e.getClosestThumbIndex=l;function v(i,o,u){i.style.transform="translate(".concat(o,"px, ").concat(u,"px)")}e.translate=v;var y=function(i){var o=[],u=null,h=function(){for(var b=[],g=0;g<arguments.length;g++)b[g]=arguments[g];o=b,!u&&(u=requestAnimationFrame(function(){u=null,i.apply(void 0,o)}))};return h};e.schd=y;function k(i,o,u){var h=i.slice(0);return h[o]=u,h}e.replaceAt=k;function E(i){var o=i.values,u=i.colors,h=i.min,b=i.max,g=i.direction,O=g===void 0?s.Direction.Right:g,_=i.rtl,P=_===void 0?!1:_;P&&O===s.Direction.Right?O=s.Direction.Left:P&&s.Direction.Left&&(O=s.Direction.Right);var I=o.slice(0).sort(function(V,W){return V-W}).map(function(V){return(V-h)/(b-h)*100}),U=I.reduce(function(V,W,A){return"".concat(V,", ").concat(u[A]," ").concat(W,"%, ").concat(u[A+1]," ").concat(W,"%")},"");return"linear-gradient(".concat(O,", ").concat(u[0]," 0%").concat(U,", ").concat(u[u.length-1]," 100%)")}e.getTrackBackground=E;function w(){}e.voidFn=w;function R(i){throw new Error("Didn't expect to get here")}e.assertUnreachable=R;var M=function(i,o,u,h,b){b===void 0&&(b=function(O){return O});var g=Math.ceil(r([i],Array.from(i.children),!0).reduce(function(O,_){var P=Math.ceil(_.getBoundingClientRect().width);if(_.innerText&&_.innerText.includes(u)&&_.childElementCount===0){var I=_.cloneNode(!0);I.innerHTML=b(o.toFixed(h)),I.style.visibility="hidden",document.body.appendChild(I),P=Math.ceil(I.getBoundingClientRect().width),document.body.removeChild(I)}return P>O?P:O},i.getBoundingClientRect().width));return g},D=function(i,o,u,h,b,g,O){O===void 0&&(O=function(I){return I});var _=[],P=function(I){var U=M(u[I],h[I],b,g,O),V=o[I].x;o.forEach(function(W,A){var L=W.x,X=M(u[A],h[A],b,g,O);I!==A&&(V>=L&&V<=L+X||V+U>=L&&V+U<=L+X)&&(_.includes(A)||(_.push(I),_.push(A),_=r(r([],_,!0),[I,A],!1),P(A)))})};return P(i),Array.from(new Set(_.sort()))},m=function(i,o,u,h,b,g){h===void 0&&(h=.1),b===void 0&&(b=" - "),g===void 0&&(g=function(A){return A});var O=(0,e.getStepDecimals)(h),_=(0,a.useState)({}),P=_[0],I=_[1],U=(0,a.useState)(g(o[u].toFixed(O))),V=U[0],W=U[1];return(0,a.useEffect)(function(){if(i){var A=i.getThumbs();if(A.length<1)return;var L={},X=i.getOffsets(),ne=D(u,X,A,o,b,O,g),le=g(o[u].toFixed(O));if(ne.length){var Q=ne.reduce(function(ee,oe,ge,pe){return ee.length?r(r([],ee,!0),[X[pe[ge]].x],!1):[X[pe[ge]].x]},[]);if(Math.min.apply(Math,Q)===X[u].x){var de=[];ne.forEach(function(ee){de.push(o[ee].toFixed(O))}),le=Array.from(new Set(de.sort(function(ee,oe){return parseFloat(ee)-parseFloat(oe)}))).map(g).join(b);var fe=Math.min.apply(Math,Q),he=Math.max.apply(Math,Q),_e=A[ne[Q.indexOf(he)]].getBoundingClientRect().width;L.left="".concat(Math.abs(fe-(he+_e))/2,"px"),L.transform="translate(-50%, 0)"}else L.visibility="hidden"}W(le),I(L)}},[i,o]),[V,P]};e.useThumbOverlap=m;function C(i,o,u,h){var b=i.getBoundingClientRect(),g=b.left,O=b.top,_=b.width,P=b.height;return F(h)?Math.abs(u-(O+P/2)):Math.abs(o-(g+_/2))}var z=function(){var i,o=((i=navigator.userAgentData)===null||i===void 0?void 0:i.platform)||navigator.platform;return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(o)||navigator.userAgent.includes("Mac")&&"ontouchend"in document};e.isIOS=z}(ue)),ue}var We;function Dt(){if(We)return H;We=1;var e=H&&H.__extends||function(){var $=function(T,d){return $=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var l in n)Object.prototype.hasOwnProperty.call(n,l)&&(t[l]=n[l])},$(T,d)};return function(T,d){if(typeof d!="function"&&d!==null)throw new TypeError("Class extends value "+String(d)+" is not a constructor or null");$(T,d);function t(){this.constructor=T}T.prototype=d===null?Object.create(d):(t.prototype=d.prototype,new t)}}(),r=H&&H.__createBinding||(Object.create?function($,T,d,t){t===void 0&&(t=d);var n=Object.getOwnPropertyDescriptor(T,d);(!n||("get"in n?!T.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return T[d]}}),Object.defineProperty($,t,n)}:function($,T,d,t){t===void 0&&(t=d),$[t]=T[d]}),a=H&&H.__setModuleDefault||(Object.create?function($,T){Object.defineProperty($,"default",{enumerable:!0,value:T})}:function($,T){$.default=T}),s=H&&H.__importStar||function($){if($&&$.__esModule)return $;var T={};if($!=null)for(var d in $)d!=="default"&&Object.prototype.hasOwnProperty.call($,d)&&r(T,$,d);return a(T,$),T},p=H&&H.__spreadArray||function($,T,d){if(d||arguments.length===2)for(var t=0,n=T.length,l;t<n;t++)(l||!(t in T))&&(l||(l=Array.prototype.slice.call(T,0,t)),l[t]=T[t]);return $.concat(l||Array.prototype.slice.call(T))};Object.defineProperty(H,"__esModule",{value:!0});var S=s(Ze()),c=Je(),f=Ie(),j=["ArrowRight","ArrowUp","k","PageUp"],F=["ArrowLeft","ArrowDown","j","PageDown"],B=function($){e(T,$);function T(d){var t=$.call(this,d)||this;if(t.trackRef=S.createRef(),t.thumbRefs=[],t.state={draggedTrackPos:[-1,-1],draggedThumbIndex:-1,thumbZIndexes:new Array(t.props.values.length).fill(0).map(function(n,l){return l}),isChanged:!1,markOffsets:[]},t.getOffsets=function(){var n=t.props,l=n.direction,v=n.values,y=n.min,k=n.max,E=t.trackRef.current;if(!E)return console.warn("No track element found."),[];var w=E.getBoundingClientRect(),R=(0,c.getPaddingAndBorder)(E);return t.getThumbs().map(function(M,D){var m={x:0,y:0},C=M.getBoundingClientRect(),z=(0,c.getMargin)(M);switch(l){case f.Direction.Right:return m.x=(z.left+R.left)*-1,m.y=((C.height-w.height)/2+R.top)*-1,m.x+=w.width*(0,c.relativeValue)(v[D],y,k)-C.width/2,m;case f.Direction.Left:return m.x=(z.right+R.right)*-1,m.y=((C.height-w.height)/2+R.top)*-1,m.x+=w.width-w.width*(0,c.relativeValue)(v[D],y,k)-C.width/2,m;case f.Direction.Up:return m.x=((C.width-w.width)/2+z.left+R.left)*-1,m.y=-R.left,m.y+=w.height-w.height*(0,c.relativeValue)(v[D],y,k)-C.height/2,m;case f.Direction.Down:return m.x=((C.width-w.width)/2+z.left+R.left)*-1,m.y=-R.left,m.y+=w.height*(0,c.relativeValue)(v[D],y,k)-C.height/2,m;default:return(0,c.assertUnreachable)(l)}})},t.getThumbs=function(){return t.trackRef&&t.trackRef.current?Array.from(t.trackRef.current.children).filter(function(n){return n.hasAttribute("aria-valuenow")}):(console.warn("No thumbs found in the track container. Did you forget to pass & spread the `props` param in renderTrack?"),[])},t.getTargetIndex=function(n){return t.getThumbs().findIndex(function(l){return l===n.target||l.contains(n.target)})},t.addTouchEvents=function(n){document.addEventListener("touchmove",t.schdOnTouchMove,{passive:!1}),document.addEventListener("touchend",t.schdOnEnd,{passive:!1}),document.addEventListener("touchcancel",t.schdOnEnd,{passive:!1})},t.addMouseEvents=function(n){document.addEventListener("mousemove",t.schdOnMouseMove),document.addEventListener("mouseup",t.schdOnEnd)},t.onMouseDownTrack=function(n){var l;if(!(n.button!==0||(0,c.isIOS)()))if(n.persist(),n.preventDefault(),t.addMouseEvents(n.nativeEvent),t.props.values.length>1&&t.props.draggableTrack){if(t.thumbRefs.some(function(y){var k;return(k=y.current)===null||k===void 0?void 0:k.contains(n.target)}))return;t.setState({draggedTrackPos:[n.clientX,n.clientY]},function(){return t.onMove(n.clientX,n.clientY)})}else{var v=(0,c.getClosestThumbIndex)(t.thumbRefs.map(function(y){return y.current}),n.clientX,n.clientY,t.props.direction);(l=t.thumbRefs[v].current)===null||l===void 0||l.focus(),t.setState({draggedThumbIndex:v},function(){return t.onMove(n.clientX,n.clientY)})}},t.onResize=function(){(0,c.translateThumbs)(t.getThumbs(),t.getOffsets(),t.props.rtl),t.calculateMarkOffsets()},t.onTouchStartTrack=function(n){var l;if(n.persist(),t.addTouchEvents(n.nativeEvent),t.props.values.length>1&&t.props.draggableTrack){if(t.thumbRefs.some(function(y){var k;return(k=y.current)===null||k===void 0?void 0:k.contains(n.target)}))return;t.setState({draggedTrackPos:[n.touches[0].clientX,n.touches[0].clientY]},function(){return t.onMove(n.touches[0].clientX,n.touches[0].clientY)})}else{var v=(0,c.getClosestThumbIndex)(t.thumbRefs.map(function(y){return y.current}),n.touches[0].clientX,n.touches[0].clientY,t.props.direction);(l=t.thumbRefs[v].current)===null||l===void 0||l.focus(),t.setState({draggedThumbIndex:v},function(){return t.onMove(n.touches[0].clientX,n.touches[0].clientY)})}},t.onMouseOrTouchStart=function(n){if(!t.props.disabled){var l=(0,c.isTouchEvent)(n);if(!(!l&&n.button!==0)){var v=t.getTargetIndex(n);v!==-1&&(l?t.addTouchEvents(n):t.addMouseEvents(n),t.setState({draggedThumbIndex:v,thumbZIndexes:t.state.thumbZIndexes.map(function(y,k){return k===v?Math.max.apply(Math,t.state.thumbZIndexes):y<=t.state.thumbZIndexes[v]?y:y-1})}))}}},t.onMouseMove=function(n){n.preventDefault(),t.onMove(n.clientX,n.clientY)},t.onTouchMove=function(n){n.preventDefault(),t.onMove(n.touches[0].clientX,n.touches[0].clientY)},t.onKeyDown=function(n){var l=t.props,v=l.values,y=l.onChange,k=l.step,E=l.rtl,w=l.direction,R=t.state.isChanged,M=t.getTargetIndex(n.nativeEvent),D=E||w===f.Direction.Left||w===f.Direction.Down?-1:1;M!==-1&&(j.includes(n.key)?(n.preventDefault(),t.setState({draggedThumbIndex:M,isChanged:!0}),y((0,c.replaceAt)(v,M,t.normalizeValue(v[M]+D*(n.key==="PageUp"?k*10:k),M)))):F.includes(n.key)?(n.preventDefault(),t.setState({draggedThumbIndex:M,isChanged:!0}),y((0,c.replaceAt)(v,M,t.normalizeValue(v[M]-D*(n.key==="PageDown"?k*10:k),M)))):n.key==="Tab"?t.setState({draggedThumbIndex:-1},function(){R&&t.fireOnFinalChange()}):R&&t.fireOnFinalChange())},t.onKeyUp=function(n){var l=t.state.isChanged;t.setState({draggedThumbIndex:-1},function(){l&&t.fireOnFinalChange()})},t.onMove=function(n,l){var v=t.state,y=v.draggedThumbIndex,k=v.draggedTrackPos,E=t.props,w=E.direction,R=E.min,M=E.max,D=E.onChange,m=E.values,C=E.step,z=E.rtl;if(y===-1&&k[0]===-1&&k[1]===-1)return null;var i=t.trackRef.current;if(!i)return null;var o=i.getBoundingClientRect(),u=(0,c.isVertical)(w)?o.height:o.width;if(k[0]!==-1&&k[1]!==-1){var h=n-k[0],b=l-k[1],g=0;switch(w){case f.Direction.Right:case f.Direction.Left:g=h/u*(M-R);break;case f.Direction.Down:case f.Direction.Up:g=b/u*(M-R);break;default:(0,c.assertUnreachable)(w)}if(z&&(g*=-1),Math.abs(g)>=C/2){for(var O=0;O<t.thumbRefs.length;O++){if(m[O]===M&&Math.sign(g)===1||m[O]===R&&Math.sign(g)===-1)return;var _=m[O]+g;_>M?g=M-m[O]:_<R&&(g=R-m[O])}for(var P=m.slice(0),O=0;O<t.thumbRefs.length;O++)P=(0,c.replaceAt)(P,O,t.normalizeValue(m[O]+g,O));t.setState({draggedTrackPos:[n,l]}),D(P)}}else{var I=0;switch(w){case f.Direction.Right:I=(n-o.left)/u*(M-R)+R;break;case f.Direction.Left:I=(u-(n-o.left))/u*(M-R)+R;break;case f.Direction.Down:I=(l-o.top)/u*(M-R)+R;break;case f.Direction.Up:I=(u-(l-o.top))/u*(M-R)+R;break;default:(0,c.assertUnreachable)(w)}z&&(I=M+R-I),Math.abs(m[y]-I)>=C/2&&D((0,c.replaceAt)(m,y,t.normalizeValue(I,y)))}},t.normalizeValue=function(n,l){var v=t.props,y=v.min,k=v.max,E=v.step,w=v.allowOverlap,R=v.values;return(0,c.normalizeValue)(n,l,y,k,E,w,R)},t.onEnd=function(n){if(n.preventDefault(),document.removeEventListener("mousemove",t.schdOnMouseMove),document.removeEventListener("touchmove",t.schdOnTouchMove),document.removeEventListener("mouseup",t.schdOnEnd),document.removeEventListener("touchend",t.schdOnEnd),document.removeEventListener("touchcancel",t.schdOnEnd),t.state.draggedThumbIndex===-1&&t.state.draggedTrackPos[0]===-1&&t.state.draggedTrackPos[1]===-1)return null;t.setState({draggedThumbIndex:-1,draggedTrackPos:[-1,-1]},function(){t.fireOnFinalChange()})},t.fireOnFinalChange=function(){t.setState({isChanged:!1});var n=t.props,l=n.onFinalChange,v=n.values;l&&l(v)},t.updateMarkRefs=function(n){if(!n.renderMark){t.numOfMarks=void 0,t.markRefs=void 0;return}t.numOfMarks=(n.max-n.min)/t.props.step,t.markRefs=[];for(var l=0;l<t.numOfMarks+1;l++)t.markRefs[l]=S.createRef()},t.calculateMarkOffsets=function(){if(!(!t.props.renderMark||!t.trackRef||!t.numOfMarks||!t.markRefs||t.trackRef.current===null)){for(var n=window.getComputedStyle(t.trackRef.current),l=parseInt(n.width,10),v=parseInt(n.height,10),y=parseInt(n.paddingLeft,10),k=parseInt(n.paddingTop,10),E=[],w=0;w<t.numOfMarks+1;w++){var R=9999,M=9999;if(t.markRefs[w].current){var D=t.markRefs[w].current.getBoundingClientRect();R=D.height,M=D.width}t.props.direction===f.Direction.Left||t.props.direction===f.Direction.Right?E.push([Math.round(l/t.numOfMarks*w+y-M/2),-Math.round((R-v)/2)]):E.push([Math.round(v/t.numOfMarks*w+k-R/2),-Math.round((M-l)/2)])}t.setState({markOffsets:E})}},d.step===0)throw new Error('"step" property should be a positive number');return t.schdOnMouseMove=(0,c.schd)(t.onMouseMove),t.schdOnTouchMove=(0,c.schd)(t.onTouchMove),t.schdOnEnd=(0,c.schd)(t.onEnd),t.thumbRefs=d.values.map(function(){return S.createRef()}),t.updateMarkRefs(d),t}return T.prototype.componentDidMount=function(){var d=this,t=this.props,n=t.values,l=t.min,v=t.step;this.resizeObserver=window.ResizeObserver?new window.ResizeObserver(this.onResize):{observe:function(){return window.addEventListener("resize",d.onResize)},unobserve:function(){return window.removeEventListener("resize",d.onResize)}},document.addEventListener("touchstart",this.onMouseOrTouchStart,{passive:!1}),document.addEventListener("mousedown",this.onMouseOrTouchStart,{passive:!1}),!this.props.allowOverlap&&(0,c.checkInitialOverlap)(this.props.values),this.props.values.forEach(function(y){return(0,c.checkBoundaries)(y,d.props.min,d.props.max)}),this.resizeObserver.observe(this.trackRef.current),(0,c.translateThumbs)(this.getThumbs(),this.getOffsets(),this.props.rtl),this.calculateMarkOffsets(),n.forEach(function(y){(0,c.isStepDivisible)(l,y,v)||console.warn("The `values` property is in conflict with the current `step`, `min`, and `max` properties. Please provide values that are accessible using the min, max, and step values.")})},T.prototype.componentDidUpdate=function(d,t){var n=this.props,l=n.max,v=n.min,y=n.step,k=n.values,E=n.rtl;(d.max!==l||d.min!==v||d.step!==y)&&this.updateMarkRefs(this.props),(0,c.translateThumbs)(this.getThumbs(),this.getOffsets(),E),(d.max!==l||d.min!==v||d.step!==y||t.markOffsets.length!==this.state.markOffsets.length)&&(this.calculateMarkOffsets(),k.forEach(function(w){(0,c.isStepDivisible)(v,w,y)||console.warn("The `values` property is in conflict with the current `step`, `min`, and `max` properties. Please provide values that are accessible using the min, max, and step values.")}))},T.prototype.componentWillUnmount=function(){var d={passive:!1};document.removeEventListener("mousedown",this.onMouseOrTouchStart,d),document.removeEventListener("mousemove",this.schdOnMouseMove),document.removeEventListener("touchmove",this.schdOnTouchMove),document.removeEventListener("touchstart",this.onMouseOrTouchStart),document.removeEventListener("mouseup",this.schdOnEnd),document.removeEventListener("touchend",this.schdOnEnd),this.resizeObserver.unobserve(this.trackRef.current)},T.prototype.render=function(){var d=this,t=this.props,n=t.label,l=t.labelledBy,v=t.renderTrack,y=t.renderThumb,k=t.renderMark,E=k===void 0?function(){return null}:k,w=t.values,R=t.min,M=t.max,D=t.allowOverlap,m=t.disabled,C=this.state,z=C.draggedThumbIndex,i=C.thumbZIndexes,o=C.markOffsets;return v({props:{style:{transform:"scale(1)",cursor:z>-1?"grabbing":this.props.draggableTrack?(0,c.isVertical)(this.props.direction)?"ns-resize":"ew-resize":w.length===1&&!m?"pointer":"inherit"},onMouseDown:m?c.voidFn:this.onMouseDownTrack,onTouchStart:m?c.voidFn:this.onTouchStartTrack,ref:this.trackRef},isDragged:this.state.draggedThumbIndex>-1,disabled:m,children:p(p([],o.map(function(u,h,b){return E({props:{style:d.props.direction===f.Direction.Left||d.props.direction===f.Direction.Right?{position:"absolute",left:"".concat(u[0],"px"),marginTop:"".concat(u[1],"px")}:{position:"absolute",top:"".concat(u[0],"px"),marginLeft:"".concat(u[1],"px")},key:"mark".concat(h),ref:d.markRefs[h]},index:h})}),!0),w.map(function(u,h){var b=d.state.draggedThumbIndex===h;return y({index:h,value:u,isDragged:b,props:{style:{position:"absolute",zIndex:i[h],cursor:m?"inherit":b?"grabbing":"grab",userSelect:"none",touchAction:"none",WebkitUserSelect:"none",MozUserSelect:"none",msUserSelect:"none"},key:h,tabIndex:m?void 0:0,"aria-valuemax":D?M:w[h+1]||M,"aria-valuemin":D?R:w[h-1]||R,"aria-valuenow":u,draggable:!1,ref:d.thumbRefs[h],"aria-label":n,"aria-labelledby":l,role:"slider",onKeyDown:m?c.voidFn:d.onKeyDown,onKeyUp:m?c.voidFn:d.onKeyUp}})}),!0)})},T.defaultProps={label:"Accessibility label",labelledBy:null,step:1,direction:f.Direction.Right,rtl:!1,disabled:!1,allowOverlap:!1,draggableTrack:!1,min:0,max:100},T}(S.Component);return H.default=B,H}var qe;function Pt(){return qe||(qe=1,function(e){var r=se&&se.__importDefault||function(S){return S&&S.__esModule?S:{default:S}};Object.defineProperty(e,"__esModule",{value:!0}),e.checkValuesAgainstBoundaries=e.relativeValue=e.useThumbOverlap=e.Direction=e.getTrackBackground=e.Range=void 0;var a=r(Dt());e.Range=a.default;var s=Je();Object.defineProperty(e,"getTrackBackground",{enumerable:!0,get:function(){return s.getTrackBackground}}),Object.defineProperty(e,"useThumbOverlap",{enumerable:!0,get:function(){return s.useThumbOverlap}}),Object.defineProperty(e,"relativeValue",{enumerable:!0,get:function(){return s.relativeValue}}),Object.defineProperty(e,"checkValuesAgainstBoundaries",{enumerable:!0,get:function(){return s.checkValuesAgainstBoundaries}});var p=Ie();Object.defineProperty(e,"Direction",{enumerable:!0,get:function(){return p.Direction}})}(se)),se}var Qe=Pt();function Ke(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);r&&(s=s.filter(function(p){return Object.getOwnPropertyDescriptor(e,p).enumerable})),a.push.apply(a,s)}return a}function ve(e){for(var r=1;r<arguments.length;r++){var a=arguments[r]!=null?arguments[r]:{};r%2?Ke(Object(a),!0).forEach(function(s){Bt(e,s,a[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):Ke(Object(a)).forEach(function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(a,s))})}return e}function Bt(e,r,a){return r in e?Object.defineProperty(e,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[r]=a,e}var be=J("div",{position:"relative",width:"100%"});be.displayName="Root";be.displayName="Root";be.displayName="StyledRoot";var ye=J("div",function(e){var r=e.$theme,a=e.$value,s=a===void 0?[]:a,p=e.$disabled,S=e.$isDragged,c=r.sizing,f="inherit";return p?f="not-allowed":S?f="grabbing":s.length===1&&(f="pointer"),{paddingTop:c.scale600,paddingBottom:c.scale600,paddingRight:c.scale600,paddingLeft:c.scale600,display:"flex",cursor:f,backgroundColor:r.colors.sliderTrackFill}});ye.displayName="Track";ye.displayName="Track";ye.displayName="StyledTrack";var Te=J("div",function(e){var r=e.$theme,a=e.$value,s=a===void 0?[]:a,p=e.$min,S=e.$max,c=e.$disabled,f=r.colors,j=r.borders,F=r.direction,B=r.borders.useRoundedCorners?j.radius100:0;return{borderTopLeftRadius:B,borderTopRightRadius:B,borderBottomRightRadius:B,borderBottomLeftRadius:B,background:Qe.getTrackBackground({values:s,colors:s.length===1?[c?f.borderOpaque:f.primary,c?f.backgroundSecondary:f.borderOpaque]:[c?f.backgroundSecondary:f.borderOpaque,c?f.borderOpaque:f.primary,c?f.backgroundSecondary:f.borderOpaque],min:p||0,max:S||0,rtl:F==="rtl"}),height:"2px",width:"100%",alignSelf:"center",cursor:c?"not-allowed":"inherit"}});Te.displayName="InnerTrack";Te.displayName="InnerTrack";Te.displayName="StyledInnerTrack";var ke=J("div",function(e){return{width:"4px",height:"2px",backgroundColor:e.$theme.colors.backgroundPrimary,marginLeft:"16px"}});ke.displayName="Mark";ke.displayName="Mark";ke.displayName="StyledMark";var Oe=J("div",function(e){return ve(ve({},e.$theme.typography.font200),{},{color:e.$theme.colors.contentPrimary})});Oe.displayName="Tick";Oe.displayName="Tick";Oe.displayName="StyledTick";var we=J("div",function(e){var r=e.$theme,a=r.sizing;return{display:"flex",justifyContent:"space-between",alignItems:"center",paddingRight:a.scale600,paddingLeft:a.scale600,paddingBottom:a.scale400}});we.displayName="TickBar";we.displayName="TickBar";we.displayName="StyledTickBar";var Re=J("div",function(e){var r=e.$theme,a=e.$value,s=a===void 0?[]:a,p=e.$thumbIndex,S=e.$disabled,c=s.length===2&&p===0,f=s.length===2&&p===1;return r.direction==="rtl"&&(f||c)&&(c=!c,f=!f),{height:"24px",width:"24px",borderTopLeftRadius:"24px",borderTopRightRadius:"24px",borderBottomLeftRadius:"24px",borderBottomRightRadius:"24px",display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:S?r.colors.sliderHandleFillDisabled:r.colors.sliderHandleFill,outline:"none",boxShadow:e.$isFocusVisible?"0 0 0 3px ".concat(r.colors.accent):"0 1px 4px rgba(0, 0, 0, 0.12)",cursor:S?"not-allowed":"inherit"}});Re.displayName="Thumb";Re.displayName="Thumb";Re.displayName="StyledThumb";var Se=J("div",function(e){var r=e.$disabled,a=e.$theme;return{position:"absolute",top:"-16px",width:"4px",height:"20px",backgroundColor:r?a.colors.sliderHandleFillDisabled:a.colors.sliderHandleInnerFill}});Se.displayName="InnerThumb";Se.displayName="InnerThumb";Se.displayName="StyledInnerThumb";var Me=J("div",function(e){var r=e.$disabled,a=e.$theme;return ve(ve({position:"absolute",top:"-".concat(a.sizing.scale1400)},a.typography.font200),{},{backgroundColor:r?a.colors.sliderHandleFillDisabled:a.colors.sliderHandleInnerFill,color:a.colors.contentInversePrimary,paddingLeft:a.sizing.scale600,paddingRight:a.sizing.scale600,paddingTop:a.sizing.scale500,paddingBottom:a.sizing.scale500,borderBottomLeftRadius:"48px",borderBottomRightRadius:"48px",borderTopLeftRadius:"48px",borderTopRightRadius:"48px",whiteSpace:"nowrap"})});Me.displayName="ThumbValue";Me.displayName="ThumbValue";Me.displayName="StyledThumbValue";function Ye(e,r){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);r&&(s=s.filter(function(p){return Object.getOwnPropertyDescriptor(e,p).enumerable})),a.push.apply(a,s)}return a}function At(e){for(var r=1;r<arguments.length;r++){var a=arguments[r]!=null?arguments[r]:{};r%2?Ye(Object(a),!0).forEach(function(s){Lt(e,s,a[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):Ye(Object(a)).forEach(function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(a,s))})}return e}function Lt(e,r,a){return r in e?Object.defineProperty(e,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[r]=a,e}function Y(){return Y=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var a=arguments[r];for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e},Y.apply(this,arguments)}function K(e,r){return Vt(e)||zt(e,r)||jt(e,r)||Ft()}function Ft(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function jt(e,r){if(e){if(typeof e=="string")return Ge(e,r);var a=Object.prototype.toString.call(e).slice(8,-1);if(a==="Object"&&e.constructor&&(a=e.constructor.name),a==="Map"||a==="Set")return Array.from(e);if(a==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return Ge(e,r)}}function Ge(e,r){(r==null||r>e.length)&&(r=e.length);for(var a=0,s=new Array(r);a<r;a++)s[a]=e[a];return s}function zt(e,r){var a=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(a!=null){var s=[],p=!0,S=!1,c,f;try{for(a=a.call(e);!(p=(c=a.next()).done)&&(s.push(c.value),!(r&&s.length===r));p=!0);}catch(j){S=!0,f=j}finally{try{!p&&a.return!=null&&a.return()}finally{if(S)throw f}}return s}}function Vt(e){if(Array.isArray(e))return e}var Nt=function(r){if(r.length>2||r.length===0)throw new Error("the value prop represents positions of thumbs, so its length can be only one or two");return r};function Ut(e){var r=e.overrides,a=r===void 0?{}:r,s=e.disabled,p=s===void 0?!1:s,S=e.marks,c=S===void 0?!1:S,f=e.onChange,j=f===void 0?function(){}:f,F=e.onFinalChange,B=F===void 0?function(){}:F,$=e.min,T=$===void 0?0:$,d=e.max,t=d===void 0?100:d,n=e.step,l=n===void 0?1:n,v=e.persistentThumb,y=v===void 0?!1:v,k=e.valueToLabel,E=k===void 0?function(G){return G}:k,w=e.value,R=x.useContext(mt),M=x.useState(!1),D=K(M,2),m=D[0],C=D[1],z=x.useState(!1),i=K(z,2),o=i[0],u=i[1],h=x.useState(!1),b=K(h,2),g=b[0],O=b[1],_=x.useState(-1),P=K(_,2),I=P[0],U=P[1],V=x.useCallback(function(G){bt(G)&&O(!0);var N=G.target.parentNode.firstChild===G.target?0:1;U(N)},[]),W=x.useCallback(function(G){g!==!1&&O(!1),U(-1)},[]),A=Nt(w),L={$disabled:p,$step:l,$min:T,$max:t,$marks:c,$value:A,$isFocusVisible:g},X=Z(a.Root,be),ne=K(X,2),le=ne[0],Q=ne[1],de=Z(a.Track,ye),fe=K(de,2),he=fe[0],_e=fe[1],ee=Z(a.InnerTrack,Te),oe=K(ee,2),ge=oe[0],pe=oe[1],et=Z(a.Thumb,Re),xe=K(et,2),tt=xe[0],rt=xe[1],nt=Z(a.InnerThumb,Se),Ce=K(nt,2),at=Ce[0],it=Ce[1],ot=Z(a.ThumbValue,Me),De=K(ot,2),st=De[0],ut=De[1],ct=Z(a.Tick,Oe),Pe=K(ct,2),Be=Pe[0],Ae=Pe[1],lt=Z(a.TickBar,we),Le=K(lt,2),dt=Le[0],ft=Le[1],ht=Z(a.Mark,ke),Fe=K(ht,2),gt=Fe[0],pt=Fe[1];return x.createElement(le,Y({"data-baseweb":"slider"},L,Q,{onFocus:Tt(Q,V),onBlur:yt(Q,W)}),x.createElement(Qe.Range,Y({step:l,min:T,max:t,values:A,disabled:p,onChange:function(N){return j({value:N})},onFinalChange:function(N){return B({value:N})},rtl:R.direction==="rtl",renderTrack:function(N){var te=N.props,q=N.children,ae=N.isDragged;return x.createElement(he,Y({onMouseDown:te.onMouseDown,onTouchStart:te.onTouchStart,$isDragged:ae},L,_e),x.createElement(ge,Y({$isDragged:ae,ref:te.ref},L,pe),q))},renderThumb:function(N){var te=N.props,q=N.index,ae=N.isDragged,je=y||(!!q&&o||!q&&m||ae)&&!p;return x.createElement(tt,Y({},te,{onMouseEnter:function(){q===0?C(!0):u(!0)},onMouseLeave:function(){q===0?C(!1):u(!1)},$thumbIndex:q,$isDragged:ae,style:At({},te.style)},L,rt,{$isFocusVisible:g&&I===q}),je&&x.createElement(st,Y({$thumbIndex:q,$isDragged:ae},L,ut),E(A[q])),je&&x.createElement(at,Y({$thumbIndex:q,$isDragged:ae},L,it)))}},c?{renderMark:function(N){var te=N.props,q=N.index;return x.createElement(gt,Y({$markIndex:q},te,L,pt))}}:{})),x.createElement(dt,Y({},L,ft),x.createElement(Be,Y({},L,Ae),E(T)),x.createElement(Be,Y({},L,Ae),E(t))))}const Ht=me("div",{target:"e8lt0n70"})(({disabled:e,theme:r})=>({alignItems:"center",backgroundColor:e?r.colors.gray:r.colors.primary,borderTopLeftRadius:"100%",borderTopRightRadius:"100%",borderBottomLeftRadius:"100%",borderBottomRightRadius:"100%",borderTopStyle:"none",borderBottomStyle:"none",borderRightStyle:"none",borderLeftStyle:"none",boxShadow:"none",display:"flex",justifyContent:"center",height:r.sizes.sliderThumb,width:r.sizes.sliderThumb,":focus":{outline:"none"},":focus-visible":{boxShadow:`0 0 0 0.2rem ${kt(r.colors.primary,.5)}`}})),Wt=me("div",{target:"e8lt0n71"})(({disabled:e,theme:r})=>({fontFamily:r.genericFonts.codeFont,fontSize:r.fontSizes.sm,color:e?r.colors.gray:r.colors.primary,top:"-1.6em",position:"absolute",whiteSpace:"nowrap",backgroundColor:r.colors.transparent,lineHeight:r.lineHeights.base,fontWeight:r.fontWeights.normal,pointerEvents:"none"})),qt=me("div",{target:"e8lt0n72"})(({theme:e})=>({fontSize:e.fontSizes.sm,paddingBottom:e.spacing.none,paddingLeft:e.spacing.none,paddingRight:e.spacing.none,paddingTop:"0.65em",justifyContent:"space-between",alignItems:"center",display:"flex"})),Xe=me("div",{target:"e8lt0n73"})(({disabled:e,theme:r})=>({lineHeight:r.lineHeights.base,fontWeight:r.fontWeights.normal,fontFamily:r.genericFonts.codeFont,color:e?r.colors.fadedText40:"inherit"})),Kt=200;function Yt({disabled:e,element:r,widgetMgr:a,fragmentId:s}){var D;const[p,S]=Ct({getStateFromWidgetMgr:Gt,getDefaultStateFromProto:Xt,getCurrStateFromProto:Zt,updateWidgetMgrState:Jt,element:r,widgetMgr:a,fragmentId:s}),[c,f]=x.useState(p),j=x.useRef(null),[F]=x.useState([]),[B]=x.useState([]),{colors:$,fonts:T,fontSizes:d,spacing:t}=wt(),n=c.map(m=>Ee(m,r)),l=Ee(r.min,r),v=Ee(r.max,r),y=r.label;x.useEffect(()=>{f(p)},[p]);const k=x.useCallback(Rt(Kt,m=>{S({value:m,fromUi:!0})}),[]),E=x.useCallback(({value:m})=>{f(m),k(m)},[k]),w=x.useCallback(()=>ze(qt,{"data-testid":"stSliderTickBar",children:[re(Xe,{disabled:e,"data-testid":"stSliderTickBarMin",children:l}),re(Xe,{disabled:e,"data-testid":"stSliderTickBarMax",children:v})]}),[l,v,e]),R=x.useCallback(Ve.forwardRef(function(C,z){const{$thumbIndex:i}=C,o=i||0;F[o]=z,B[o]||(B[o]=Ve.createRef());const u=St(C,["role","style","aria-valuemax","aria-valuemin","aria-valuenow","tabIndex","onKeyUp","onKeyDown","onMouseEnter","onMouseLeave","draggable"]),h=n[o];return re(Ht,{...u,disabled:C.$disabled===!0,ref:F[o],"aria-valuetext":h,"aria-label":y,children:re(Wt,{"data-testid":"stSliderThumbValue",disabled:C.$disabled===!0,ref:B[o],children:h})})}),[]);x.useEffect(()=>{var u,h;B.map((b,g)=>{b.current&&(b.current.innerText=n[g])}),F.map((b,g)=>{b.current&&b.current.setAttribute("aria-valuetext",n[g])});const m=j.current??null,C=F[0].current,z=(u=F[1])==null?void 0:u.current,i=B[0].current,o=(h=B[1])==null?void 0:h.current;tr(m,C,z,i,o)});const M=x.useCallback(({$disabled:m})=>({height:t.twoXS,...m?{background:$.darkenedBgMix25}:{}}),[$,t]);return ze("div",{ref:j,className:"stSlider","data-testid":"stSlider",children:[re(It,{label:r.label,disabled:e,labelVisibility:Mt((D=r.labelVisibility)==null?void 0:D.value),children:r.help&&re(_t,{children:re($t,{content:r.help,placement:Et.TOP_RIGHT})})}),re(Ut,{min:r.min,max:r.max,step:r.step,value:er(c,r),onChange:E,disabled:e,overrides:{Thumb:R,Tick:{style:{fontFamily:T.monospace}},Track:{style:{backgroundColor:"none !important",paddingBottom:t.none,paddingLeft:t.none,paddingRight:t.none,paddingTop:`calc(${d.sm} * 1.35)`}},InnerTrack:{style:M},TickBar:w}})]})}function Gt(e,r){return e.getDoubleArrayValue(r)}function Xt(e){return e.default}function Zt(e){return e.value}function Jt(e,r,a,s){r.setDoubleArrayValue(e,a.value,{fromUi:a.fromUi},s)}function Qt(e){const{dataType:r}=e;return r===$e.DataType.DATETIME||r===$e.DataType.DATE||r===$e.DataType.TIME}function Ee(e,r){const{format:a,options:s}=r;return Qt(r)?xt.utc(e/1e3).format(a):s.length>0?Ne.sprintf(a,s[e]):Ne.sprintf(a,e)}function er(e,r){const{min:a,max:s}=r;let p=e[0],S=e.length>1?e[1]:e[0];return p>S&&(p=S),p<a&&(p=a),p>s&&(p=s),S<a&&(S=a),S>s&&(S=s),e.length>1?[p,S]:[p]}function tr(e,r,a,s,p){!e||!r||!s||(ie(e,r,s),a&&p&&(ie(e,a,p),rr(e,r,a,s,p)))}function ie(e,r,a){const s=e.getBoundingClientRect(),p=r.getBoundingClientRect(),S=a.getBoundingClientRect(),c=p.left+p.width/2,f=c-S.width/2<s.left,j=c+S.width/2>s.right;a.style.left=f?"0":"",a.style.right=j?"0":""}function rr(e,r,a,s,p){const c=e.getBoundingClientRect(),f=r.getBoundingClientRect(),j=a.getBoundingClientRect(),F=s.getBoundingClientRect(),B=p.getBoundingClientRect(),$=c.left+c.width/2,T=f.left+f.width/2,d=j.left+j.width/2,t=T-F.width/2>=c.left,n=d+B.width/2<=c.right,l=f.left-F.width>=c.left,v=j.right+B.width<=c.right,y=t?F.width/2:F.width,k=n?B.width/2:B.width,E=T+y;if(d-k-E>24){ie(e,r,s),ie(e,a,p);return}if(l&&v){s.style.left="",s.style.right=`${Math.round(f.width)}px`,p.style.left=`${Math.round(j.width)}px`,p.style.right="";return}T<$?(ie(e,r,s),p.style.left=`${Math.round(T+y+24-d)}px`,p.style.right=""):(ie(e,a,p),s.style.left="",s.style.right=`${-Math.round(d-k-24-T)}px`)}const sr=Ot(x.memo(Yt));export{sr as default};
